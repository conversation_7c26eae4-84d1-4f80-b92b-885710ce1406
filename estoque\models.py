from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.db.models import Sum, Count, F, Q, Avg
from django.core.cache import cache
from datetime import timedelta
from decimal import Decimal
import numpy as np
from django.db.models.functions import TruncMonth, TruncWeek
from .utils import extrair_valor_numerico_diametro

# Create your models here.

class MaterialPadrao(models.Model):
    """Modelo para tipos padrão de materiais"""
    nome = models.CharField(max_length=100, help_text="Nome do material padrão")
    diametro = models.CharField(max_length=50, help_text="Diâmetro do material padrão (ex: Ø 0.40 mm)")
    data_cadastro = models.DateTimeField(auto_now_add=True)
    data_atualizacao = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.nome} - {self.diametro}"

    class Meta:
        verbose_name = "Material Padrão"
        verbose_name_plural = "Materiais Padrão"
        ordering = ['nome', 'diametro']
        indexes = [
            models.Index(fields=['nome']),
            models.Index(fields=['diametro']),
        ]

class Material(models.Model):
    """Modelo para materiais utilizados na fabricação de molas"""
    material_padrao = models.ForeignKey(
        MaterialPadrao,
        on_delete=models.PROTECT,
        related_name='materiais',
        null=True,
        blank=True,
        help_text="Material padrão associado"
    )
    nome = models.CharField(max_length=100, help_text="Nome do material")
    descricao = models.TextField(blank=True, null=True, help_text="Descrição detalhada do material")
    diametro = models.CharField(max_length=50, help_text="Diâmetro do material (ex: Ø 0.40 mm)")
    quantidade_estoque = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Quantidade em estoque em kg"
    )
    estoque_minimo = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Quantidade mínima de estoque em kg"
    )
    fornecedor = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Nome do fornecedor do material"
    )
    data = models.DateField(
        blank=True,
        null=True,
        help_text="Data de aquisição do material"
    )
    nota_fiscal = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="Número da nota fiscal"
    )
    data_cadastro = models.DateTimeField(auto_now_add=True)
    data_atualizacao = models.DateTimeField(auto_now=True)
    ativo = models.BooleanField(default=True, help_text='Indica se o material está ativo no sistema')

    def __str__(self):
        return f"{self.nome} - {self.diametro}"

    def get_diametro_numerico(self):
        """
        Extrai o valor numérico do diâmetro para ordenação correta.
        Exemplo: "0.40 mm" -> 0.4, "1.20 mm" -> 1.2
        """
        return extrair_valor_numerico_diametro(self.diametro)

    class Meta:
        verbose_name = "Material"
        verbose_name_plural = "Materiais"
        ordering = ['nome', 'diametro']
        indexes = [
            models.Index(fields=['nome']),
            models.Index(fields=['diametro']),
            models.Index(fields=['quantidade_estoque']),
            models.Index(fields=['fornecedor']),
            models.Index(fields=['data']),
        ]

    def verificar_estoque_minimo(self):
        """Verifica se o estoque está abaixo do mínimo"""
        return self.quantidade_estoque <= self.estoque_minimo

    def calcular_necessidade_producao(self, quantidade_molas, peso_por_mola):
        """Calcula se há material suficiente para produzir uma quantidade de molas"""
        # Converter para Decimal para evitar problemas de tipo
        material_necessario = Decimal(str(quantidade_molas)) * Decimal(str(peso_por_mola))
        return {
            'suficiente': self.quantidade_estoque >= material_necessario,
            'disponivel': self.quantidade_estoque,
            'necessario': material_necessario,
            'faltante': max(Decimal('0'), material_necessario - self.quantidade_estoque)
        }


class Mola(models.Model):
    """Modelo para molas fabricadas"""
    codigo = models.CharField(
        max_length=50,
        unique=True,
        help_text="Código único da mola"
    )
    nome_mola = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text="Número da mola (após a '/' no código)"
    )
    cliente = models.CharField(
        max_length=100,
        help_text="Nome do cliente"
    )
    descricao = models.TextField(
        blank=True,
        null=True,
        help_text="Descrição detalhada da mola"
    )
    material = models.ForeignKey(
        Material,
        on_delete=models.PROTECT,
        related_name='molas',
        null=True,
        blank=True,
        help_text="Material utilizado na fabricação da mola"
    )
    material_padrao = models.ForeignKey(
        MaterialPadrao,
        on_delete=models.PROTECT,
        related_name='molas',
        null=True,
        blank=True,
        help_text="Tipo de material padrão utilizado na fabricação da mola"
    )
    diametro = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="Diâmetro da mola"
    )
    volumes = models.IntegerField(
        blank=True,
        null=True,
        help_text="Número de volumes"
    )
    quantidade_por_volume = models.IntegerField(
        blank=True,
        null=True,
        help_text="Quantidade de molas por volume"
    )
    quantidade_estoque = models.IntegerField(
        default=0,
        help_text="Quantidade atual em estoque"
    )
    estoque_minimo = models.IntegerField(
        default=0,
        help_text="Quantidade mínima de estoque"
    )
    ordem_fabricacao = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="Número da ordem de fabricação"
    )
    data = models.DateField(
        blank=True,
        null=True,
        help_text="Data de fabricação"
    )
    peso_unitario = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        blank=True,
        null=True,
        help_text="Peso unitário da mola em gramas (medido manualmente)"
    )
    data_cadastro = models.DateTimeField(auto_now_add=True)
    data_atualizacao = models.DateTimeField(auto_now=True)
    ativo = models.BooleanField(
        default=True,
        help_text="Indica se a mola está ativa no sistema"
    )

    def __str__(self):
        return f"{self.codigo} - {self.cliente}"

    def save(self, *args, **kwargs):
        """Método save padrão"""
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Mola"
        verbose_name_plural = "Molas"
        ordering = ['codigo']
        indexes = [
            models.Index(fields=['codigo']),
            models.Index(fields=['nome_mola']),
            models.Index(fields=['cliente']),
            models.Index(fields=['material']),
            models.Index(fields=['quantidade_estoque']),
            models.Index(fields=['data']),
            models.Index(fields=['data_cadastro']),
        ]

    def verificar_estoque_minimo(self):
        """Verifica se o estoque está abaixo do mínimo"""
        return self.quantidade_estoque <= self.estoque_minimo

    @classmethod
    def buscar_por_nome_e_cliente(cls, nome_mola, cliente):
        """Busca uma mola pelo nome e cliente"""
        return cls.objects.filter(nome_mola=nome_mola, cliente=cliente).first()

    def verificar_material_suficiente(self, quantidade):
        """Verifica se há material suficiente para produzir a quantidade especificada"""
        # Se não houver material associado, não é possível verificar
        if not self.material:
            return {
                'suficiente': False,
                'disponivel': 0,
                'necessario': 0,
                'faltante': 0,
                'erro': 'Nenhum material associado à mola'
            }

        # Usar o peso unitário informado ou um valor padrão se não estiver disponível
        if self.peso_unitario:
            # Converter de gramas para quilogramas (dividir por 1000)
            peso_por_mola = self.peso_unitario / Decimal('1000')
        else:
            # Usar valor padrão se não houver peso unitário informado (1g = 0.001kg)
            peso_por_mola = Decimal('0.001')

        return self.material.calcular_necessidade_producao(quantidade, peso_por_mola)

    @classmethod
    def mais_vendidas(cls, periodo=None, data_inicial=None, data_final=None):
        """Retorna as molas mais vendidas em um período específico"""
        hoje = timezone.now().date()

        # Define o filtro de período
        if periodo == 'personalizado' and data_inicial and data_final:
            # Usar datas personalizadas fornecidas
            data_inicio = data_inicial
            data_fim = data_final
        elif periodo == 'semana':
            data_inicio = hoje - timezone.timedelta(days=7)
            data_fim = hoje
        elif periodo == 'mes':
            data_inicio = hoje.replace(day=1)
            data_fim = hoje
        elif periodo == '3meses':
            data_inicio = (hoje.replace(day=1) - timezone.timedelta(days=1)).replace(day=1)
            data_inicio = (data_inicio - timezone.timedelta(days=1)).replace(day=1)
            data_fim = hoje
        elif periodo == '6meses':
            data_inicio = (hoje.replace(day=1) - timezone.timedelta(days=1)).replace(day=1)
            for _ in range(4):
                data_inicio = (data_inicio - timezone.timedelta(days=1)).replace(day=1)
            data_fim = hoje
        elif periodo == 'ano':
            data_inicio = hoje.replace(month=1, day=1)
            data_fim = hoje
        else:
            # Se não especificar período, considera todo o histórico
            data_inicio = None
            data_fim = None

        # Consulta as movimentações de saída no período (vendas que movimentaram estoque)
        query_movimentacoes = MovimentacaoEstoque.objects.filter(tipo='S')
        if data_inicio:
            query_movimentacoes = query_movimentacoes.filter(data__gte=data_inicio)
        if data_fim:
            query_movimentacoes = query_movimentacoes.filter(data__lte=data_fim)

        # Agrupa por mola e soma as quantidades das movimentações
        resultado_movimentacoes = query_movimentacoes.values('mola').annotate(
            total_vendido=Sum('quantidade'),
            mola_codigo=F('mola__codigo'),
            mola_cliente=F('mola__cliente')
        )

        # Consulta os itens de pedido atendidos que não movimentaram estoque
        query_itens_pedido = ItemPedido.objects.filter(
            atendido=True,
            movimentar_estoque=False
        )
        if data_inicio:
            query_itens_pedido = query_itens_pedido.filter(pedido__data_pedido__gte=data_inicio)
        if data_fim:
            query_itens_pedido = query_itens_pedido.filter(pedido__data_pedido__lte=data_fim)

        # Agrupa por mola e soma as quantidades dos itens de pedido
        resultado_itens_pedido = query_itens_pedido.values('mola').annotate(
            total_vendido=Sum('quantidade'),
            mola_codigo=F('mola__codigo'),
            mola_cliente=F('mola__cliente')
        )

        # Combina os resultados das duas consultas
        molas_vendidas = {}

        # Adiciona as vendas que movimentaram estoque
        for item in resultado_movimentacoes:
            mola_id = item['mola']
            molas_vendidas[mola_id] = {
                'mola': mola_id,
                'total_vendido': item['total_vendido'],
                'mola_codigo': item['mola_codigo'],
                'mola_cliente': item['mola_cliente']
            }

        # Adiciona as vendas que não movimentaram estoque
        for item in resultado_itens_pedido:
            mola_id = item['mola']
            if mola_id in molas_vendidas:
                # Se a mola já existe, soma a quantidade
                molas_vendidas[mola_id]['total_vendido'] += item['total_vendido']
            else:
                # Se a mola não existe, adiciona ao dicionário
                molas_vendidas[mola_id] = {
                    'mola': mola_id,
                    'total_vendido': item['total_vendido'],
                    'mola_codigo': item['mola_codigo'],
                    'mola_cliente': item['mola_cliente']
                }

        # Calcular média mensal e variação para cada mola
        for mola_id, dados in molas_vendidas.items():
            # Calcular média mensal
            media_mensal = cls.calcular_media_mensal_vendas(mola_id, data_inicio, data_fim)
            dados['media_mensal'] = media_mensal

            # Calcular variação percentual
            variacao = cls.calcular_variacao_vendas(mola_id, data_inicio, data_fim)
            dados['variacao_percentual'] = variacao

            # Calcular variação em relação à média histórica
            variacao_media_historica = cls.calcular_variacao_media_historica(mola_id, dados['total_vendido'])
            dados['variacao_media_historica'] = variacao_media_historica

        # Converte o dicionário para lista e ordena por total_vendido
        resultado = list(molas_vendidas.values())
        resultado.sort(key=lambda x: x['total_vendido'], reverse=True)

        return resultado

    @classmethod
    def _calcular_numero_meses_periodo(cls, data_inicio, data_fim):
        """Calcula o número de meses entre duas datas (inclusive)"""
        if not data_inicio or not data_fim:
            return 0

        # Calcular a diferença em meses
        anos_diff = data_fim.year - data_inicio.year
        meses_diff = data_fim.month - data_inicio.month

        # Total de meses no período (inclusive)
        total_meses = anos_diff * 12 + meses_diff + 1

        return max(1, total_meses)  # Garantir que seja pelo menos 1 mês

    @classmethod
    def calcular_media_mensal_vendas(cls, mola_id, data_inicio=None, data_fim=None):
        """Calcula a média mensal de vendas para uma mola no período especificado"""
        # Consulta as movimentações de saída (vendas que movimentaram estoque)
        query_movimentacoes = MovimentacaoEstoque.objects.filter(mola_id=mola_id, tipo='S')
        if data_inicio:
            query_movimentacoes = query_movimentacoes.filter(data__gte=data_inicio)
        if data_fim:
            query_movimentacoes = query_movimentacoes.filter(data__lte=data_fim)

        # Consulta os itens de pedido atendidos que não movimentaram estoque
        query_itens_pedido = ItemPedido.objects.filter(
            mola_id=mola_id,
            atendido=True,
            movimentar_estoque=False
        )
        if data_inicio:
            query_itens_pedido = query_itens_pedido.filter(pedido__data_pedido__gte=data_inicio)
        if data_fim:
            query_itens_pedido = query_itens_pedido.filter(pedido__data_pedido__lte=data_fim)

        # Agrupar vendas por mês
        vendas_mensais = {}

        # Processar movimentações de estoque
        for mov in query_movimentacoes:
            mes_ano = mov.data.strftime('%Y-%m')
            if mes_ano not in vendas_mensais:
                vendas_mensais[mes_ano] = 0
            vendas_mensais[mes_ano] += mov.quantidade

        # Processar itens de pedido sem movimentação de estoque
        for item in query_itens_pedido:
            mes_ano = item.pedido.data_pedido.strftime('%Y-%m')
            if mes_ano not in vendas_mensais:
                vendas_mensais[mes_ano] = 0
            vendas_mensais[mes_ano] += item.quantidade

        # Calcular média mensal
        total_vendas = sum(vendas_mensais.values())

        # Se não há vendas, retorna 0
        if total_vendas == 0:
            return 0

        # Calcular o número correto de meses no período
        num_meses = cls._calcular_numero_meses_periodo(data_inicio, data_fim)

        return round(total_vendas / num_meses) if num_meses > 0 else 0

    @classmethod
    def calcular_variacao_vendas(cls, mola_id, data_inicio=None, data_fim=None):
        """Calcula a variação percentual de vendas em relação ao período anterior"""
        hoje = timezone.now().date()

        # Se não houver datas definidas, retorna zero
        if not data_inicio or not data_fim:
            return 0

        # Calcular a duração do período em dias
        duracao_periodo = (data_fim - data_inicio).days
        if duracao_periodo <= 0:
            return 0

        # Definir o período anterior com a mesma duração
        data_fim_anterior = data_inicio - timezone.timedelta(days=1)
        data_inicio_anterior = data_fim_anterior - timezone.timedelta(days=duracao_periodo)

        # Calcular vendas no período atual
        vendas_periodo_atual = cls._calcular_total_vendas(mola_id, data_inicio, data_fim)

        # Calcular vendas no período anterior
        vendas_periodo_anterior = cls._calcular_total_vendas(mola_id, data_inicio_anterior, data_fim_anterior)

        # Calcular variação percentual
        if vendas_periodo_anterior == 0:
            return 100 if vendas_periodo_atual > 0 else 0

        variacao = ((vendas_periodo_atual - vendas_periodo_anterior) / vendas_periodo_anterior) * 100

        return round(variacao, 1)

    @classmethod
    def calcular_media_historica_vendas(cls, mola_id):
        """Calcula a média histórica de vendas de uma mola considerando todo o histórico"""
        # Consulta as movimentações de saída (vendas que movimentaram estoque)
        movimentacoes = MovimentacaoEstoque.objects.filter(
            mola_id=mola_id,
            tipo='S'
        ).values('data__year', 'data__month').annotate(
            total_mes=Sum('quantidade')
        )

        # Consulta os itens de pedido atendidos que não movimentaram estoque
        itens_pedido = ItemPedido.objects.filter(
            mola_id=mola_id,
            atendido=True,
            movimentar_estoque=False
        ).values('pedido__data_pedido__year', 'pedido__data_pedido__month').annotate(
            total_mes=Sum('quantidade')
        )

        # Combinar vendas por mês
        vendas_mensais = {}

        for mov in movimentacoes:
            chave = f"{mov['data__year']}-{mov['data__month']:02d}"
            vendas_mensais[chave] = vendas_mensais.get(chave, 0) + mov['total_mes']

        for item in itens_pedido:
            chave = f"{item['pedido__data_pedido__year']}-{item['pedido__data_pedido__month']:02d}"
            vendas_mensais[chave] = vendas_mensais.get(chave, 0) + item['total_mes']

        # Calcular média histórica
        if not vendas_mensais:
            return 0

        total_vendas = sum(vendas_mensais.values())
        num_meses = len(vendas_mensais)

        return round(total_vendas / num_meses) if num_meses > 0 else 0

    @classmethod
    def calcular_variacao_media_historica(cls, mola_id, vendas_periodo):
        """Calcula a variação percentual em relação à média histórica"""
        media_historica = cls.calcular_media_historica_vendas(mola_id)

        if media_historica == 0:
            return 100 if vendas_periodo > 0 else 0

        variacao = ((vendas_periodo - media_historica) / media_historica) * 100
        return round(variacao, 1)

    @classmethod
    def _calcular_total_vendas(cls, mola_id, data_inicio, data_fim):
        """Método auxiliar para calcular o total de vendas em um período"""
        # Consulta as movimentações de saída (vendas que movimentaram estoque)
        total_movimentacoes = MovimentacaoEstoque.objects.filter(
            mola_id=mola_id,
            tipo='S',
            data__gte=data_inicio,
            data__lte=data_fim
        ).aggregate(total=Sum('quantidade'))['total'] or 0

        # Consulta os itens de pedido atendidos que não movimentaram estoque
        total_itens_pedido = ItemPedido.objects.filter(
            mola_id=mola_id,
            atendido=True,
            movimentar_estoque=False,
            pedido__data_pedido__gte=data_inicio,
            pedido__data_pedido__lte=data_fim
        ).aggregate(total=Sum('quantidade'))['total'] or 0

        return total_movimentacoes + total_itens_pedido

    @classmethod
    def obter_vendas_mensais(cls, periodo=None, data_inicial=None, data_final=None, limite=10):
        """Retorna dados de vendas mensais para gráfico de tendência"""
        hoje = timezone.now().date()

        # Define o filtro de período
        if periodo == 'personalizado' and data_inicial and data_final:
            data_inicio = data_inicial
            data_fim = data_final
        elif periodo == 'semana':
            data_inicio = hoje - timezone.timedelta(days=7)
            data_fim = hoje
        elif periodo == 'mes':
            data_inicio = hoje.replace(day=1)
            data_fim = hoje
        elif periodo == '3meses':
            data_inicio = (hoje.replace(day=1) - timezone.timedelta(days=1)).replace(day=1)
            data_inicio = (data_inicio - timezone.timedelta(days=1)).replace(day=1)
            data_fim = hoje
        elif periodo == '6meses':
            data_inicio = (hoje.replace(day=1) - timezone.timedelta(days=1)).replace(day=1)
            for _ in range(4):
                data_inicio = (data_inicio - timezone.timedelta(days=1)).replace(day=1)
            data_fim = hoje
        elif periodo == 'ano':
            data_inicio = hoje.replace(month=1, day=1)
            data_fim = hoje
        else:
            # Se não especificar período, usa os últimos 12 meses
            data_inicio = hoje.replace(day=1) - timezone.timedelta(days=365)
            data_fim = hoje

        # Consulta as movimentações de saída no período
        query_movimentacoes = MovimentacaoEstoque.objects.filter(tipo='S')
        if data_inicio:
            query_movimentacoes = query_movimentacoes.filter(data__gte=data_inicio)
        if data_fim:
            query_movimentacoes = query_movimentacoes.filter(data__lte=data_fim)

        # Agrupar por mês usando TruncMonth
        vendas_por_mes = query_movimentacoes.annotate(
            mes=TruncMonth('data')
        ).values('mes').annotate(
            total=Sum('quantidade')
        ).order_by('mes')

        # Consulta os itens de pedido atendidos que não movimentaram estoque
        query_itens_pedido = ItemPedido.objects.filter(
            atendido=True,
            movimentar_estoque=False
        )
        if data_inicio:
            query_itens_pedido = query_itens_pedido.filter(pedido__data_pedido__gte=data_inicio)
        if data_fim:
            query_itens_pedido = query_itens_pedido.filter(pedido__data_pedido__lte=data_fim)

        # Agrupar por mês usando TruncMonth
        vendas_pedidos_por_mes = query_itens_pedido.annotate(
            mes=TruncMonth('pedido__data_pedido')
        ).values('mes').annotate(
            total=Sum('quantidade')
        ).order_by('mes')

        # Combinar os resultados
        vendas_mensais = {}

        # Processar movimentações de estoque
        for item in vendas_por_mes:
            mes_str = item['mes'].strftime('%Y-%m')
            vendas_mensais[mes_str] = item['total']

        # Processar itens de pedido sem movimentação de estoque
        for item in vendas_pedidos_por_mes:
            mes_str = item['mes'].strftime('%Y-%m')
            if mes_str in vendas_mensais:
                vendas_mensais[mes_str] += item['total']
            else:
                vendas_mensais[mes_str] = item['total']

        # Ordenar por mês
        vendas_ordenadas = sorted(vendas_mensais.items())

        # Preparar dados para o gráfico
        labels = [f"{mes[5:7]}/{mes[0:4]}" for mes, _ in vendas_ordenadas]  # Formato MM/YYYY
        valores = [total for _, total in vendas_ordenadas]

        return {
            'labels': labels,
            'valores': valores
        }

    @classmethod
    def mais_vendidas_periodo_personalizado(cls, data_inicio, data_fim):
        """Retorna as molas mais vendidas em um período específico personalizado"""
        # Consulta as movimentações de saída no período
        query_movimentacoes = MovimentacaoEstoque.objects.filter(
            tipo='S',
            data__gte=data_inicio,
            data__lte=data_fim
        ).values('mola').annotate(
            total_vendido=Sum('quantidade')
        ).order_by('-total_vendido')

        # Consulta os itens de pedido atendidos que não movimentaram estoque
        query_itens_pedido = ItemPedido.objects.filter(
            atendido=True,
            movimentar_estoque=False,
            pedido__data_pedido__gte=data_inicio,
            pedido__data_pedido__lte=data_fim
        ).values('mola').annotate(
            total_vendido=Sum('quantidade')
        ).order_by('-total_vendido')

        # Combinar os resultados
        molas_vendidas = {}

        # Processar movimentações de estoque
        for item in query_movimentacoes:
            mola_id = item['mola']
            mola = cls.objects.get(id=mola_id)
            molas_vendidas[mola_id] = {
                'mola': mola_id,
                'mola_codigo': mola.codigo,
                'mola_cliente': mola.cliente,
                'total_vendido': item['total_vendido']
            }

        # Processar itens de pedido sem movimentação de estoque
        for item in query_itens_pedido:
            mola_id = item['mola']
            if mola_id in molas_vendidas:
                molas_vendidas[mola_id]['total_vendido'] += item['total_vendido']
            else:
                mola = cls.objects.get(id=mola_id)
                molas_vendidas[mola_id] = {
                    'mola': mola_id,
                    'mola_codigo': mola.codigo,
                    'mola_cliente': mola.cliente,
                    'total_vendido': item['total_vendido']
                }

        # Converte o dicionário para lista e ordena por total_vendido
        resultado = list(molas_vendidas.values())
        resultado.sort(key=lambda x: x['total_vendido'], reverse=True)

        return resultado


class MovimentacaoEstoque(models.Model):
    """Modelo para registrar movimentações de estoque de molas"""
    TIPO_CHOICES = [
        ('E', 'Entrada'),
        ('S', 'Saída'),
    ]

    mola = models.ForeignKey(Mola, on_delete=models.CASCADE, related_name='movimentacoes')
    tipo = models.CharField(max_length=1, choices=TIPO_CHOICES)
    quantidade = models.IntegerField()
    ordem_venda = models.CharField(max_length=50, blank=True, null=True)
    observacao = models.TextField(blank=True, null=True)
    data = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.get_tipo_display()} de {self.quantidade} unidades de {self.mola.codigo}"

    def save(self, *args, **kwargs):
        # Garantir que o campo data tenha um timezone
        if self.data and timezone.is_naive(self.data):
            self.data = timezone.make_aware(self.data)
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Movimentação de Estoque"
        verbose_name_plural = "Movimentações de Estoque"
        ordering = ['-data']
        indexes = [
            models.Index(fields=['mola']),
            models.Index(fields=['tipo']),
            models.Index(fields=['data']),
            models.Index(fields=['ordem_venda']),
            models.Index(fields=['tipo', 'mola']),
            models.Index(fields=['tipo', 'data']),
        ]

    def clean(self):
        """Validação personalizada para movimentações"""
        # Validar quantidade
        if self.quantidade <= 0:
            raise ValidationError("A quantidade deve ser maior que zero.")

        # Validar estoque para saídas
        if self.tipo == 'S' and self.quantidade > self.mola.quantidade_estoque:
            raise ValidationError(f"Quantidade insuficiente em estoque. Disponível: {self.mola.quantidade_estoque}, Solicitado: {self.quantidade}")

        # Validar tipo
        if self.tipo not in ['E', 'S']:
            raise ValidationError("Tipo de movimentação inválido. Use 'E' para entrada ou 'S' para saída.")

    def save(self, *args, **kwargs):
        """Sobrescreve o método save para atualizar o estoque da mola"""
        # Primeiro salva a movimentação
        super().save(*args, **kwargs)

        # Atualiza o estoque da mola
        mola = self.mola
        if self.tipo == 'E':
            mola.quantidade_estoque += self.quantidade
        else:  # Saída
            mola.quantidade_estoque -= self.quantidade
        mola.save()

        # Invalidar cache relacionado a movimentações
        # Limpar cache do dashboard que mostra movimentações recentes
        cache.delete('dashboard_data')
        # Limpar cache da lista de molas (que mostra quantidade em estoque)
        cache.delete('mola_list_queryset')


class MovimentacaoMaterial(models.Model):
    """Modelo para registrar movimentações de estoque de materiais"""
    TIPO_CHOICES = [
        ('E', 'Entrada'),
        ('S', 'Saída'),
    ]

    material = models.ForeignKey(Material, on_delete=models.CASCADE, related_name='movimentacoes')
    tipo = models.CharField(max_length=1, choices=TIPO_CHOICES)
    quantidade = models.DecimalField(max_digits=10, decimal_places=2)
    ordem_compra = models.CharField(max_length=50, blank=True, null=True)
    observacao = models.TextField(blank=True, null=True)
    data = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.get_tipo_display()} de {self.quantidade} kg de {self.material.nome}"

    def save(self, *args, **kwargs):
        # Garantir que o campo data tenha um timezone
        if self.data and timezone.is_naive(self.data):
            self.data = timezone.make_aware(self.data)
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Movimentação de Material"
        verbose_name_plural = "Movimentações de Material"
        ordering = ['-data']
        indexes = [
            models.Index(fields=['material']),
            models.Index(fields=['tipo']),
            models.Index(fields=['data']),
            models.Index(fields=['ordem_compra']),
            models.Index(fields=['tipo', 'material']),
            models.Index(fields=['tipo', 'data']),
        ]

    def clean(self):
        """Validação personalizada para movimentações"""
        # Validar quantidade
        if self.quantidade <= 0:
            raise ValidationError("A quantidade deve ser maior que zero.")

        # Validar estoque para saídas
        if self.tipo == 'S' and self.quantidade > self.material.quantidade_estoque:
            raise ValidationError(f"Quantidade insuficiente em estoque. Disponível: {self.material.quantidade_estoque} kg, Solicitado: {self.quantidade} kg")

        # Validar tipo
        if self.tipo not in ['E', 'S']:
            raise ValidationError("Tipo de movimentação inválido. Use 'E' para entrada ou 'S' para saída.")

    def save(self, *args, **kwargs):
        """Sobrescreve o método save para atualizar o estoque do material"""
        # Primeiro salva a movimentação
        super().save(*args, **kwargs)

        # Atualiza o estoque do material
        material = self.material
        if self.tipo == 'E':
            material.quantidade_estoque += self.quantidade
        else:  # Saída
            material.quantidade_estoque -= self.quantidade
        material.save()


class PedidoVenda(models.Model):
    """Modelo para pedidos de venda"""
    STATUS_CHOICES = [
        ('P', 'Pendente'),
        ('A', 'Aprovado'),
        ('C', 'Cancelado'),
        ('F', 'Finalizado'),
    ]

    numero_pedido = models.CharField(max_length=50, unique=True)
    cliente = models.CharField(max_length=100)
    data_pedido = models.DateField()
    status = models.CharField(max_length=1, choices=STATUS_CHOICES, default='P')
    observacao = models.TextField(blank=True, null=True)
    data_cadastro = models.DateTimeField(auto_now_add=True)
    data_atualizacao = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Pedido {self.numero_pedido} - {self.cliente}"

    class Meta:
        verbose_name = "Pedido de Venda"
        verbose_name_plural = "Pedidos de Venda"
        ordering = ['-data_pedido']
        indexes = [
            models.Index(fields=['numero_pedido']),
            models.Index(fields=['cliente']),
            models.Index(fields=['data_pedido']),
            models.Index(fields=['status']),
            models.Index(fields=['status', 'data_pedido']),
        ]


class ItemPedido(models.Model):
    """Modelo para itens de um pedido de venda"""
    pedido = models.ForeignKey(PedidoVenda, on_delete=models.CASCADE, related_name='itens')
    mola = models.ForeignKey(Mola, on_delete=models.PROTECT)
    quantidade = models.IntegerField()
    atendido = models.BooleanField(default=False)
    movimentar_estoque = models.BooleanField(default=True, help_text="Se marcado, a quantidade será deduzida do estoque quando o pedido for aprovado")

    def __str__(self):
        return f"{self.quantidade} unidades de {self.mola.codigo}"

    class Meta:
        verbose_name = "Item de Pedido"
        verbose_name_plural = "Itens de Pedido"

    def processar_saida(self):
        """Processa a saída do estoque para este item de pedido"""
        # Se não deve movimentar estoque, apenas marca como atendido
        if not self.movimentar_estoque and not self.atendido:
            self.atendido = True
            self.save()
            return True

        # Se deve movimentar estoque, verifica disponibilidade e cria movimentação
        if not self.atendido and self.movimentar_estoque and self.mola.quantidade_estoque >= self.quantidade:
            # Cria a movimentação de saída
            MovimentacaoEstoque.objects.create(
                mola=self.mola,
                tipo='S',
                quantidade=self.quantidade,
                ordem_venda=self.pedido.numero_pedido,
                observacao=f"Saída automática para o pedido {self.pedido.numero_pedido}"
            )

            # Marca o item como atendido
            self.atendido = True
            self.save()

            return True
        return False


class PrevisaoDemanda(models.Model):
    """Modelo para armazenar previsões de demanda"""
    PERIODO_CHOICES = (
        ('S', 'Semanal'),
        ('M', 'Mensal'),
        ('T', 'Trimestral'),
    )

    METODO_CHOICES = (
        ('MM', 'Média Móvel'),
        ('SE', 'Suavização Exponencial'),
        ('RL', 'Regressão Linear'),
    )

    mola = models.ForeignKey(Mola, on_delete=models.CASCADE, related_name='previsoes')
    data_previsao = models.DateField(auto_now_add=True)
    periodo = models.CharField(max_length=1, choices=PERIODO_CHOICES, default='M')
    metodo = models.CharField(max_length=2, choices=METODO_CHOICES, default='MM')
    quantidade_prevista = models.IntegerField()
    data_inicio = models.DateField()
    data_fim = models.DateField()
    precisao = models.FloatField(null=True, blank=True)  # Precisão da previsão anterior (0-100%)

    def __str__(self):
        return f"Previsão {self.mola.codigo} - {self.get_periodo_display()} ({self.data_inicio} a {self.data_fim})"

    class Meta:
        verbose_name = "Previsão de Demanda"
        verbose_name_plural = "Previsões de Demanda"
        ordering = ['-data_previsao']
        indexes = [
            models.Index(fields=['mola']),
            models.Index(fields=['data_previsao']),
            models.Index(fields=['periodo']),
            models.Index(fields=['metodo']),
            models.Index(fields=['data_inicio']),
            models.Index(fields=['data_fim']),
            models.Index(fields=['mola', 'periodo']),
            models.Index(fields=['mola', 'data_previsao']),
        ]

    @classmethod
    def calcular_media_movel(cls, mola_id, janela=3):
        """Calcula previsão usando média móvel"""
        # Obter movimentações de saída (vendas) da mola
        movimentacoes = MovimentacaoEstoque.objects.filter(
            mola_id=mola_id,
            tipo='S'  # Saída
        ).order_by('-data')

        # Agrupar por mês
        vendas_mensais = {}
        for mov in movimentacoes:
            mes_ano = mov.data.strftime('%Y-%m')
            if mes_ano not in vendas_mensais:
                vendas_mensais[mes_ano] = 0
            vendas_mensais[mes_ano] += mov.quantidade

        # Converter para lista ordenada
        vendas_lista = [(k, v) for k, v in sorted(vendas_mensais.items(), reverse=True)]

        if len(vendas_lista) < janela:
            return None

        # Calcular média móvel
        ultimas_vendas = [v for _, v in vendas_lista[:janela]]
        media = sum(ultimas_vendas) / janela

        return round(media)

    @classmethod
    def gerar_previsao(cls, mola_id, metodo='MM', periodo='M'):
        """Gera uma previsão de demanda para uma mola"""
        mola = Mola.objects.get(id=mola_id)
        hoje = timezone.now().date()

        # Definir datas de início e fim com base no período
        if periodo == 'S':
            data_inicio = hoje + timedelta(days=1)
            data_fim = hoje + timedelta(days=7)
        elif periodo == 'M':
            data_inicio = hoje + timedelta(days=1)
            data_fim = hoje + timedelta(days=30)
        else:  # Trimestral
            data_inicio = hoje + timedelta(days=1)
            data_fim = hoje + timedelta(days=90)

        # Calcular previsão com base no método
        if metodo == 'MM':
            janela = 3  # Últimos 3 meses
            quantidade = cls.calcular_media_movel(mola_id, janela=janela)
        elif metodo == 'SE':
            # Implementação simplificada de suavização exponencial
            # Em um sistema real, usaríamos bibliotecas como statsmodels
            alpha = 0.3  # Fator de suavização
            vendas_recentes = MovimentacaoEstoque.objects.filter(
                mola_id=mola_id,
                tipo='S'
            ).order_by('-data')[:5]

            if not vendas_recentes:
                return None

            ultima_venda = vendas_recentes[0].quantidade
            penultima_venda = vendas_recentes[1].quantidade if len(vendas_recentes) > 1 else ultima_venda

            quantidade = alpha * ultima_venda + (1 - alpha) * penultima_venda
            quantidade = round(quantidade)
        else:  # Regressão Linear
            # Implementação simplificada
            # Em um sistema real, usaríamos scikit-learn
            movimentacoes = MovimentacaoEstoque.objects.filter(
                mola_id=mola_id,
                tipo='S'
            ).order_by('data')

            if movimentacoes.count() < 5:
                return None

            # Extrair dados para regressão
            x = list(range(1, movimentacoes.count() + 1))
            y = [m.quantidade for m in movimentacoes]

            # Calcular coeficientes da regressão linear
            n = len(x)
            x_mean = sum(x) / n
            y_mean = sum(y) / n

            numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n))
            denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

            slope = numerator / denominator
            intercept = y_mean - (slope * x_mean)

            # Prever próximo valor
            next_x = n + 1
            quantidade = round(intercept + slope * next_x)

            # Garantir que a previsão não seja negativa
            quantidade = max(0, quantidade)

        # Criar e salvar a previsão
        if quantidade is not None:
            previsao = cls.objects.create(
                mola=mola,
                periodo=periodo,
                metodo=metodo,
                quantidade_prevista=quantidade,
                data_inicio=data_inicio,
                data_fim=data_fim
            )
            return previsao

        return None


class AnaliseObsolescencia(models.Model):
    """Modelo para análise de obsolescência de itens"""
    CLASSIFICACAO_CHOICES = (
        ('A', 'Alta Rotatividade'),
        ('B', 'Média Rotatividade'),
        ('C', 'Baixa Rotatividade'),
        ('D', 'Sem Movimentação'),
        ('O', 'Obsoleto'),
    )

    mola = models.ForeignKey(Mola, on_delete=models.CASCADE, related_name='analises_obsolescencia')
    data_analise = models.DateField(auto_now_add=True)
    classificacao = models.CharField(max_length=1, choices=CLASSIFICACAO_CHOICES)
    dias_sem_movimentacao = models.IntegerField()
    ultima_movimentacao = models.DateField(null=True, blank=True)
    valor_estoque = models.DecimalField(max_digits=10, decimal_places=2)
    recomendacao = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Análise {self.mola.codigo} - {self.get_classificacao_display()}"

    class Meta:
        verbose_name = "Análise de Obsolescência"
        verbose_name_plural = "Análises de Obsolescência"
        ordering = ['-data_analise']
        indexes = [
            models.Index(fields=['mola']),
            models.Index(fields=['data_analise']),
            models.Index(fields=['classificacao']),
            models.Index(fields=['dias_sem_movimentacao']),
            models.Index(fields=['ultima_movimentacao']),
            models.Index(fields=['mola', 'classificacao']),
        ]

    @classmethod
    def classificar_item(cls, mola_id):
        """Classifica um item com base em sua movimentação"""
        mola = Mola.objects.get(id=mola_id)

        # Obter última movimentação
        ultima_mov = MovimentacaoEstoque.objects.filter(
            mola_id=mola_id
        ).order_by('-data').first()

        if not ultima_mov:
            # Nunca teve movimentação
            if mola.data_cadastro:
                dias_sem_mov = (timezone.now().date() - mola.data_cadastro.date()).days
            else:
                # Se não tiver data de cadastro, assume 30 dias
                dias_sem_mov = 30
            classificacao = 'D'  # Sem Movimentação
            ultima_data = None
        else:
            # Converter datetime para date se necessário
            ultima_data = ultima_mov.data.date() if hasattr(ultima_mov.data, 'date') else ultima_mov.data
            dias_sem_mov = (timezone.now().date() - ultima_data).days

            # Classificar com base nos dias sem movimentação
            if dias_sem_mov <= 30:
                classificacao = 'A'  # Alta Rotatividade
            elif dias_sem_mov <= 90:
                classificacao = 'B'  # Média Rotatividade
            elif dias_sem_mov <= 180:
                classificacao = 'C'  # Baixa Rotatividade
            elif dias_sem_mov <= 365:
                classificacao = 'D'  # Sem Movimentação
            else:
                classificacao = 'O'  # Obsoleto

        # Calcular valor em estoque usando o peso unitário informado
        if mola.peso_unitario:
            # Converter de gramas para quilogramas (dividir por 1000)
            custo_medio = mola.peso_unitario / Decimal('1000')
        else:
            # Usar valor padrão se não houver peso unitário informado (1g = 0.001kg)
            custo_medio = Decimal('0.001')

        valor_estoque = Decimal(str(mola.quantidade_estoque)) * custo_medio

        # Gerar recomendação
        if classificacao == 'O':
            recomendacao = "Considerar descarte ou promoção especial para liquidação."
        elif classificacao == 'D':
            recomendacao = "Avaliar redução do estoque mínimo e monitorar demanda."
        elif classificacao == 'C':
            recomendacao = "Revisar estoque mínimo e considerar promoções."
        else:
            recomendacao = "Manter política atual de estoque."

        # Criar e salvar a análise
        analise = cls.objects.create(
            mola=mola,
            classificacao=classificacao,
            dias_sem_movimentacao=dias_sem_mov,
            ultima_movimentacao=ultima_data,
            valor_estoque=valor_estoque,
            recomendacao=recomendacao
        )

        return analise

    @classmethod
    def analisar_todos_itens(cls):
        """Analisa todos os itens do estoque"""
        molas = Mola.objects.all()
        resultados = []

        for mola in molas:
            analise = cls.classificar_item(mola.id)
            resultados.append(analise)

        return resultados


class PlanejamentoProducao(models.Model):
    """Modelo para ordem de fabricação"""
    STATUS_CHOICES = (
        ('P', 'Pendente'),
        ('E', 'Em Produção'),
        ('C', 'Concluído'),
        ('A', 'Atrasado'),
        ('X', 'Cancelado'),
    )

    PRIORIDADE_CHOICES = (
        (1, 'Baixa'),
        (2, 'Normal'),
        (3, 'Alta'),
        (4, 'Urgente'),
    )

    nome = models.CharField(max_length=100)
    descricao = models.TextField(blank=True, null=True)
    data_criacao = models.DateTimeField(auto_now_add=True)
    data_inicio = models.DateField()
    data_fim_prevista = models.DateField()
    data_fim_real = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=1, choices=STATUS_CHOICES, default='P')
    prioridade = models.IntegerField(choices=PRIORIDADE_CHOICES, default=2)
    responsavel = models.CharField(max_length=100, blank=True, null=True)
    observacoes = models.TextField(blank=True, null=True)
    material = models.ForeignKey(
        Material,
        on_delete=models.PROTECT,
        related_name='ordens_fabricacao',
        null=True,
        blank=True,
        help_text="Material selecionado para a produção"
    )

    def __str__(self):
        return f"Ordem {self.nome} - {self.get_status_display()}"

    class Meta:
        verbose_name = "Ordem de Fabricação"
        verbose_name_plural = "Ordens de Fabricação"
        ordering = ['-data_criacao']

    def calcular_progresso(self):
        """Calcula o percentual de progresso do plano"""
        itens = self.itens.all()
        if not itens:
            return 0

        total_itens = itens.count()
        itens_concluidos = itens.filter(status='C').count()

        return (itens_concluidos / total_itens) * 100 if total_itens > 0 else 0

    def verificar_atraso(self):
        """Verifica se o plano está atrasado e atualiza o status"""
        if self.status in ['P', 'E']:
            hoje = timezone.now().date()
            # Usamos a data_fim_prevista para compatibilidade, mas podemos ajustar a lógica no futuro
            if hoje > self.data_fim_prevista:
                self.status = 'A'
                self.save(update_fields=['status'])
                return True
        return False

    def calcular_necessidade_materiais(self):
        """Calcula a necessidade de materiais para o plano"""
        necessidades = {}

        for item in self.itens.all():
            mola = item.mola
            material = mola.material

            # Pular se não houver material associado
            if not material:
                continue

            # Quantidade de material necessária por mola
            if mola.peso_unitario:
                # Converter de gramas para quilogramas (dividir por 1000)
                qtd_material_por_mola = mola.peso_unitario / Decimal('1000')
            else:
                # Usar valor padrão se não houver peso unitário informado (1g = 0.001kg)
                qtd_material_por_mola = Decimal('0.001')

            # Quantidade total de material necessária
            qtd_necessaria = Decimal(str(item.quantidade)) * qtd_material_por_mola

            # Adiciona à necessidade total
            if material.id in necessidades:
                necessidades[material.id]['quantidade'] += qtd_necessaria
            else:
                necessidades[material.id] = {
                    'material': material,
                    'quantidade': qtd_necessaria,
                    'disponivel': material.quantidade_estoque,
                    'faltante': max(Decimal('0'), qtd_necessaria - material.quantidade_estoque)
                }

        return necessidades

    @classmethod
    def gerar_plano_automatico(cls, data_inicio, dias_duracao=7, pedidos_ids=None, previsoes_ids=None):
        """Gera um plano de produção automático com base em pedidos e previsões"""
        hoje = timezone.now().date()

        # Validar data de início
        if data_inicio < hoje:
            data_inicio = hoje

        data_fim = data_inicio + timezone.timedelta(days=dias_duracao)

        # Criar o plano
        plano = cls.objects.create(
            nome=f"Plano Automático {data_inicio.strftime('%d/%m/%Y')}",
            descricao="Plano gerado automaticamente pelo sistema",
            data_inicio=data_inicio,
            data_fim_prevista=data_fim,
            status='P',
            prioridade=2
        )

        # Adicionar itens de pedidos pendentes
        if pedidos_ids:
            pedidos = PedidoVenda.objects.filter(id__in=pedidos_ids, status='P')
        else:
            # Se não especificado, usa todos os pedidos pendentes
            pedidos = PedidoVenda.objects.filter(status='P')

        for pedido in pedidos:
            for item_pedido in pedido.itens.filter(atendido=False):
                ItemPlanejamento.objects.create(
                    planejamento=plano,
                    mola=item_pedido.mola,
                    quantidade=item_pedido.quantidade,
                    origem=f"Pedido #{pedido.numero_pedido}",
                    prioridade=3,  # Alta prioridade para pedidos
                    status='P'
                )

        # Adicionar itens de previsões de demanda
        if previsoes_ids:
            previsoes = PrevisaoDemanda.objects.filter(id__in=previsoes_ids)
        else:
            # Se não especificado, usa previsões recentes
            # Obter todas as previsões recentes
            todas_previsoes = PrevisaoDemanda.objects.filter(
                data_fim__gte=hoje
            ).order_by('mola', '-data_previsao')

            # Filtrar para obter apenas a previsão mais recente para cada mola
            molas_processadas = set()
            previsoes = []

            for previsao in todas_previsoes:
                if previsao.mola_id not in molas_processadas:
                    previsoes.append(previsao)
                    molas_processadas.add(previsao.mola_id)

        for previsao in previsoes:
            # Verificar se já existe estoque suficiente
            estoque_atual = previsao.mola.quantidade_estoque
            if estoque_atual < previsao.quantidade_prevista:
                # Adicionar apenas a quantidade faltante
                quantidade_faltante = previsao.quantidade_prevista - estoque_atual
                if quantidade_faltante > 0:
                    ItemPlanejamento.objects.create(
                        planejamento=plano,
                        mola=previsao.mola,
                        quantidade=quantidade_faltante,
                        origem=f"Previsão #{previsao.id}",
                        prioridade=2,  # Prioridade normal para previsões
                        status='P'
                    )

        return plano


class BackupMovimentacaoEstoque(models.Model):
    """Modelo para armazenar backups de movimentações de estoque excluídas"""
    mola_id = models.IntegerField()
    mola_codigo = models.CharField(max_length=50)
    mola_cliente = models.CharField(max_length=100)
    tipo = models.CharField(max_length=1)
    quantidade = models.IntegerField()
    ordem_venda = models.CharField(max_length=50, blank=True, null=True)
    observacao = models.TextField(blank=True, null=True)
    data_original = models.DateTimeField()
    data_backup = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        tipo_display = "Entrada" if self.tipo == "E" else "Saída"
        return f"Backup de {tipo_display} de {self.quantidade} unidades de {self.mola_codigo}"

    class Meta:
        verbose_name = "Backup de Movimentação de Estoque"
        verbose_name_plural = "Backups de Movimentações de Estoque"
        ordering = ['-data_backup']

    @classmethod
    def criar_backup(cls, movimentacao):
        """Cria um backup de uma movimentação de estoque"""
        return cls.objects.create(
            mola_id=movimentacao.mola.id,
            mola_codigo=movimentacao.mola.codigo,
            mola_cliente=movimentacao.mola.cliente,
            tipo=movimentacao.tipo,
            quantidade=movimentacao.quantidade,
            ordem_venda=movimentacao.ordem_venda,
            observacao=movimentacao.observacao,
            data_original=movimentacao.data
        )

    def restaurar(self):
        """Restaura a movimentação a partir do backup"""
        try:
            mola = Mola.objects.get(id=self.mola_id)

            # Criar nova movimentação com os dados do backup
            movimentacao = MovimentacaoEstoque.objects.create(
                mola=mola,
                tipo=self.tipo,
                quantidade=self.quantidade,
                ordem_venda=self.ordem_venda,
                observacao=f"{self.observacao} (Restaurado de backup)"
            )

            # Excluir o backup após restauração
            self.delete()

            return movimentacao
        except Mola.DoesNotExist:
            return None


class BackupMovimentacaoMaterial(models.Model):
    """Modelo para armazenar backups de movimentações de material excluídas"""
    material_id = models.IntegerField()
    material_nome = models.CharField(max_length=100)
    material_diametro = models.CharField(max_length=50)
    tipo = models.CharField(max_length=1)
    quantidade = models.DecimalField(max_digits=10, decimal_places=2)
    ordem_compra = models.CharField(max_length=50, blank=True, null=True)
    observacao = models.TextField(blank=True, null=True)
    data_original = models.DateTimeField()
    data_backup = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        tipo_display = "Entrada" if self.tipo == "E" else "Saída"
        return f"Backup de {tipo_display} de {self.quantidade} kg de {self.material_nome}"

    class Meta:
        verbose_name = "Backup de Movimentação de Material"
        verbose_name_plural = "Backups de Movimentações de Material"
        ordering = ['-data_backup']

    @classmethod
    def criar_backup(cls, movimentacao):
        """Cria um backup de uma movimentação de material"""
        return cls.objects.create(
            material_id=movimentacao.material.id,
            material_nome=movimentacao.material.nome,
            material_diametro=movimentacao.material.diametro,
            tipo=movimentacao.tipo,
            quantidade=movimentacao.quantidade,
            ordem_compra=movimentacao.ordem_compra,
            observacao=movimentacao.observacao,
            data_original=movimentacao.data
        )

    def restaurar(self):
        """Restaura a movimentação a partir do backup"""
        try:
            material = Material.objects.get(id=self.material_id)

            # Criar nova movimentação com os dados do backup
            movimentacao = MovimentacaoMaterial.objects.create(
                material=material,
                tipo=self.tipo,
                quantidade=self.quantidade,
                ordem_compra=self.ordem_compra,
                observacao=f"{self.observacao} (Restaurado de backup)"
            )

            # Excluir o backup após restauração
            self.delete()

            return movimentacao
        except Material.DoesNotExist:
            return None


class ItemPlanejamento(models.Model):
    """Modelo para itens de uma ordem de fabricação"""
    STATUS_CHOICES = (
        ('P', 'Pendente'),
        ('E', 'Em Produção'),
        ('C', 'Concluído'),
        ('X', 'Cancelado'),
    )

    PRIORIDADE_CHOICES = (
        (1, 'Baixa'),
        (2, 'Normal'),
        (3, 'Alta'),
        (4, 'Urgente'),
    )

    planejamento = models.ForeignKey(PlanejamentoProducao, on_delete=models.CASCADE, related_name='itens')
    mola = models.ForeignKey(Mola, on_delete=models.PROTECT)
    quantidade = models.IntegerField()
    quantidade_produzida = models.IntegerField(default=0)
    data_inicio = models.DateField(null=True, blank=True)
    data_conclusao = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=1, choices=STATUS_CHOICES, default='P')
    prioridade = models.IntegerField(choices=PRIORIDADE_CHOICES, default=2)
    origem = models.CharField(max_length=100, blank=True, null=True)  # Pedido, Previsão, etc.
    observacoes = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.mola.codigo} - {self.quantidade} unidades"

    class Meta:
        verbose_name = "Item de Ordem de Fabricação"
        verbose_name_plural = "Itens de Ordem de Fabricação"
        ordering = ['-prioridade', 'data_inicio']

    def calcular_progresso(self):
        """Calcula o percentual de progresso do item"""
        if self.quantidade == 0:
            return 0
        return (self.quantidade_produzida / self.quantidade) * 100

    def iniciar_producao(self):
        """Inicia a produção do item"""
        if self.status == 'P':
            self.status = 'E'
            self.data_inicio = timezone.now().date()
            self.save(update_fields=['status', 'data_inicio'])

            # Atualiza o status do planejamento e a data de início se necessário
            if self.planejamento.status == 'P':
                self.planejamento.status = 'E'
                self.planejamento.data_inicio = timezone.now().date()
                self.planejamento.save(update_fields=['status', 'data_inicio'])

            return True
        return False

    def registrar_producao(self, quantidade):
        """Registra a produção de uma quantidade do item"""
        if self.status != 'C':
            self.quantidade_produzida += quantidade

            # Verifica se a produção foi concluída
            if self.quantidade_produzida >= self.quantidade:
                self.status = 'C'
                self.data_conclusao = timezone.now().date()

            self.save()

            # Atualiza o estoque
            MovimentacaoEstoque.objects.create(
                mola=self.mola,
                tipo='E',
                quantidade=quantidade,
                observacao=f"Produção da ordem de fabricação #{self.planejamento.id}"
            )

            return True
        return False


